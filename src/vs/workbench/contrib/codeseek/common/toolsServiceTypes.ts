import { CancellationToken } from '../../../../base/common/cancellation.js';
import { URI } from '../../../../base/common/uri.js';
import { ISearchResult } from './codebaseTypes.js';
import { Plan } from './remoteAgentServiceType.js';

export type ToolCallType = {
	name: ToolName | string;
	params: ToolCallParamsType[ToolName] | Plan;
	id: string;
};

export const enum ToolCallStatus {
	executing = "executing",
	success = "success",
	failure = "failure",
}

export type ToolCallResultType = {
	status: ToolCallStatus;
	name: ToolName;
	content: string;
	toolCallReturn: ToolCallReturnType[ToolName] | null;
	error: string | undefined;
};

export enum AskReponseType {
	yesButtonClicked = 'yesButtonClicked',
	noButtonClicked = 'noButtonClicked',
	messageResponse = 'messageResponse',
}

export type AskResponse = {
	type: string;
	response?: AskReponseType;
	text?: string;
};

export type InternalToolInfo = {
	name: string;
	description: string;
	params: {
		[paramName: string]: { type: string; description: string | undefined }; // name -> type
	};
	required: string[]; // required paramNames
	needApprove: boolean;
	using: string;
	example: string;
};

// 定义工具名称枚举
export enum ToolNameEnum {
	READ_FILE = 'read_file',
	EDIT_FILE = 'edit_file',
	LIST_FILES = 'list_files',
	PATHNAME_SEARCH = 'pathname_search',
	SEARCH = 'search',
	CREATE_FILE = 'create_file',
	UPDATE_FILE = 'update_file',
	APPROVE_REQUEST = 'approve_request',
	ASK_FOLLOWUP_QUESTION = 'ask_followup_question',
	CTAGS_QUERY = 'ctags_query',
	CLANGD_QUERY = 'clangd_query',
	SHOW_SUMMARY = 'show_summary',
	SHOW_CONTENT = 'show_content',
	CODEBASE_SEARCH = 'codebase_search',
	EXEC_COMMAND = 'exec_command',
}

export const ORIGINAL = `<<<<<<< ORIGINAL`
export const DIVIDER = `=======`
export const FINAL = `>>>>>>> UPDATED`

const searchReplaceBlockTemplate = `\
${ORIGINAL}
// ... original code goes here
${DIVIDER}
// ... final code goes here
${FINAL}

${ORIGINAL}
// ... original code goes here
${DIVIDER}
// ... final code goes here
${FINAL}`


// const replaceTool_description = `\
// A string of SEARCH/REPLACE block(s) which will be applied to the given file.
// Your SEARCH/REPLACE blocks string must be formatted as follows:
// ${searchReplaceBlockTemplate}

// ## Guidelines:

// 1. You may output multiple search replace blocks if needed.

// 2. The ORIGINAL code in each SEARCH/REPLACE block must EXACTLY match lines in the original file. Do not add or remove any whitespace or comments from the original code.

// 3. Each ORIGINAL text must be large enough to uniquely identify the change. However, bias towards writing as little as possible.

// 4. Each ORIGINAL text must be DISJOINT from all other ORIGINAL text.

// 5. This field is a STRING (not an array).`

export const codeseekTools = {
	[ToolNameEnum.READ_FILE]: {
		name: ToolNameEnum.READ_FILE,
		description: `请求读取指定相对路径下的文件内容。当您需要查看一个未知内容的现有文件时使用此功能，例如分析代码、审阅文本文件或从配置文件中提取信息。可能不适合其他类型的二进制文件，因为它将原始内容作为字符串返回。`,
		params: {
			path: { type: 'string', description: "要读取的文件的相对路径（相对于当前工作目录）" },
		},
		required: ['path'],
		needApprove: false,
		using: `\`\`\`
<read_file>
<path>此处填写文件路径</path>
</read_file>
\`\`\``,
		example: `\`\`\`
<read_file>
<path>example_dir/example.py</path>
</read_file>
\`\`\``
	},
	[ToolNameEnum.EDIT_FILE]: {
		name: ToolNameEnum.EDIT_FILE,
		description: `编辑文件，智能的把修改内容插入到文件中，不需要提供修改内容对应的起始行号。您必须提供文件的相对路径以及对应文件的修改内容。`,
		params: {
			path: { type: 'string', description: "要编辑的文件的相对路径（相对于当前工作目录）" },
			changeStr: { type: 'string', description: "文件的修改内容" },
		},
		required: ['path', 'changeStr'],
		needApprove: false,
		using: '',
		example: ''
	},
	[ToolNameEnum.LIST_FILES]: {
		name: ToolNameEnum.LIST_FILES,
		description: `请求列出指定目录中的所有文件和子目录。`,
		params: {
			path: { type: 'string', description: "要列出内容的目录路径（相对于当前工作目录）" },
		},
		required: ['path'],
		needApprove: false,
		using: '',
		example: ''
	},

	[ToolNameEnum.PATHNAME_SEARCH]: {
		name: ToolNameEnum.PATHNAME_SEARCH,
		description: `Returns all pathnames that match a given grep query. You should use this when looking for a file with a specific name or path. This does NOT search file content.`,
		params: {
			query: { type: 'string', description: "" },
		},
		required: ['query'],
		needApprove: false,
		using: '',
		example: ''
	},

	[ToolNameEnum.SEARCH]: {
		name: ToolNameEnum.SEARCH,
		description: `Returns all code excerpts containing the given string or grep query. This does NOT search pathname. As a follow-up, you may want to use read_file to view the full file contents of the results. `,
		params: {
			query: { type: 'string', description: "" },
		},
		required: ['query'],
		needApprove: false,
		using: '',
		example: ''
	},

	[ToolNameEnum.CREATE_FILE]: {
		name: ToolNameEnum.CREATE_FILE,
		description: `Creates a new file at the given URI with the given content.`,
		params: {
			path: { type: 'string', description: "" },
			content: { type: 'string', description: "" },
		},
		required: ['path', 'content'],
		needApprove: true,
		using: '',
		example: ''
	},

	[ToolNameEnum.UPDATE_FILE]: {
		name: ToolNameEnum.UPDATE_FILE,
		description: `Updates the file at the given URI with the given content.`,
		params: {
			path: { type: 'string', description: "" },
			content: { type: 'string', description: "" },
			start: { type: 'number', description: "" },
			end: { type: 'number', description: "" },
		},
		required: ['uri', 'content', 'start', 'end'],
		needApprove: true,
		using: '',
		example: ''
	},

	[ToolNameEnum.APPROVE_REQUEST]: {
		name: ToolNameEnum.APPROVE_REQUEST,
		description: `Attemps to complete the given text at the given position in the given file.`,
		params: {
			content: { type: 'string', description: 'The result from a previous attempt at completion.' },
			command: { type: 'string', description: 'Command to demonstrate result (optional)' },
		},
		required: ['content'],
		needApprove: false,
		using: '',
		example: ''
	},

	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: {
		name: ToolNameEnum.ASK_FOLLOWUP_QUESTION,
		description: `向用户提问以收集完成任务所需额外信息。当您遇到歧义、需要澄清或需要更多细节以有效进行时，应使用此工具。它通过允许与用户直接沟通，实现互动解决问题。谨慎使用此工具，以在收集必要信息和避免过多来回沟通之间保持平衡`,
		params: {
			question: { type: 'string', description: '向用户提出的问题。针对你需要的信息，提出一个清晰、具体的问题。' },
			follow_up: {
				type: 'string', description: `2-4个建议答案的列表，这些答案应在逻辑上承接问题，按优先级或逻辑顺序排列。每个建议必须:
        1. 放在自己的<suggest>标签中
        2. 具体、可操作且与当前用户问题直接相关
        3. 是问题的完整答案 - 用户不应需要提供额外信息或填补任何缺失细节。请勿包含带括号或圆括号的占位符。` },
		},
		required: ['question', 'follow_up'],
		needApprove: false,
		using: `\`\`\`
<ask_followup_question>
<question>你的问题</question>
<follow_up>
<suggest>
你的建议答案
</suggest>
</follow_up>
</ask_followup_question>
\`\`\``,
		example: `[用户问题] 在frontend-config.json文件中添加http-proxy配置信息

\`\`\`
<ask_followup_question>
<question>frontend-config.json文件的路径是什么?</question>
<follow_up>
<suggest>./src/frontend-config.json</suggest>
<suggest>./config/frontend-config.json</suggest>
<suggest>./frontend-config.json</suggest>
</follow_up>
</ask_followup_question>
\`\`\``
	},

	[ToolNameEnum.CTAGS_QUERY]: {
		name: ToolNameEnum.CTAGS_QUERY,
		description: `Use the ctags tool to output the file path, type, and starting line and column number of a given code symbol.`,
		params: {
			symbol: { type: 'string', description: 'The code symbol to query.' },
		},
		required: ['symbol'],
		needApprove: false,
		using: '',
		example: ''
	},
	[ToolNameEnum.CLANGD_QUERY]: {
		name: ToolNameEnum.CLANGD_QUERY,
		description: `Use the clangd tool to output the symbol references.`,
		params: {
			filePath: { type: 'string', description: 'The file path to query.' },
			line: { type: 'number', description: 'The symbol line number.' },
			character: { type: 'number', description: 'The symbol character number.' },
		},
		required: ['filePath', 'line', 'character'],
		needApprove: false,
		using: '',
		example: ''
	},
	[ToolNameEnum.SHOW_SUMMARY]: {
		name: ToolNameEnum.SHOW_SUMMARY,
		description: `Using folding tags to display detailed information and summaries to users.`,
		params: {
			summary: { type: 'string', description: 'The summary of the information.' },
			detail: { type: 'string', description: 'The detail of the information.' },
		},
		required: ['summary', 'detail'],
		needApprove: false,
		using: '',
		example: ''
	},
	[ToolNameEnum.SHOW_CONTENT]: {
		name: ToolNameEnum.SHOW_CONTENT,
		description: `将生成的代码或答案展示给用户。该工具用于没有目标路径，仅做生成的代码或答案最后展示的情况。`,
		params: {
			content: { type: 'string', description: '待展示的代码片段或答案。若答案中包含代码片段，使用Markdown代码块标注代码内容。' }
		},
		required: ['content'],
		needApprove: false,
		using: `\`\`\`
<show_content>
<content>此处填写待展示的代码片段或答案。若答案中包含代码片段，使用Markdown代码块标注代码内容。</content>
</show_content>
\`\`\``,
		example: `[用户问题] main函数的实现逻辑是什么？

\`\`\`
<show_content>
<content>
main函数的实现逻辑如下：\`\`\`cpp
int main() {
    int x = 0; // 初始化变量
    std::cout << x << std::endl;
    return 0;
}
\`\`\`
</content>
</show_content>
\`\`\``
	},
	[ToolNameEnum.CODEBASE_SEARCH]: {
		name: ToolNameEnum.CODEBASE_SEARCH,
		description: `从代码库中查找与搜索查询最相关的代码片段。这是一个语义搜索工具，因此查询应该在语义上与所需内容匹配。`,
		params: {
			query: { type: 'string', description: '用于查找相关代码的搜索查询。除非有明确的理由，否则您应该重复使用用户的精确查询及其措辞。' },
		},
		required: ['query'],
		needApprove: false,
		using: '',
		example: ''
	},
	[ToolNameEnum.EXEC_COMMAND]: {
		name: ToolNameEnum.EXEC_COMMAND,
		description: `Execute a command on the terminal and return the execution result.`,
		params: {
			workdir: { type: 'string', description: 'Path to execute command.' },
			command: { type: 'string', description: 'Executed commands.' },
		},
		required: ['command'],
		needApprove: true,
		using: '',
		example: ''
	},
} satisfies { [key in ToolNameEnum]: InternalToolInfo };

export type ToolName = ToolNameEnum;

export const toolNamesSet = new Set<string>(Object.values(ToolNameEnum));
export const isAToolName = (toolName: string): toolName is ToolName => {
	const isAToolName = toolNamesSet.has(toolName);
	return isAToolName;
};


export type ToolParamNames<T extends ToolName> = keyof typeof codeseekTools[T]['params'];
export type ToolParamsObj<T extends ToolName> = { [paramName in ToolParamNames<T>]: unknown };


export type ToolCallParamsType = {
	[ToolNameEnum.READ_FILE]: { path: string };
	[ToolNameEnum.EDIT_FILE]: { path: string, changeStr: string };
	[ToolNameEnum.LIST_FILES]: { path: string };
	[ToolNameEnum.PATHNAME_SEARCH]: { query: string };
	[ToolNameEnum.SEARCH]: { query: string };
	[ToolNameEnum.CREATE_FILE]: { path: string; content: string };
	[ToolNameEnum.UPDATE_FILE]: { path: string; content: string; start: number; end: number };
	[ToolNameEnum.APPROVE_REQUEST]: { content: string; command: string };
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: { question: string };
	[ToolNameEnum.CTAGS_QUERY]: { symbol: string };
	[ToolNameEnum.CLANGD_QUERY]: { filePath: string, line: number, character: number };
	[ToolNameEnum.SHOW_SUMMARY]: { summary: string, detail: string };
	[ToolNameEnum.SHOW_CONTENT]: { content: string };
	[ToolNameEnum.CODEBASE_SEARCH]: { query: string };
	[ToolNameEnum.EXEC_COMMAND]: { workdir?: string, command: string };
};


export type ReadFileResultType = { uri: URI; fileContents: string; startLine: number; endLine: number };
export type ListFilesResultType = { rootURI: URI; children: DirectoryItem[] | null; hasPrevPage: boolean; itemsRemaining: number };
export type PathnameSearchResultType = { queryStr: string; uris: URI[]; };
export type SearchResultType = { queryStr: string; uris: URI[]; };
export type CreateFileResultType = {};
export type UpdateToFileResultType = { content: string; uri: URI };
export type ApproveRequestResultType = { content: string; response: AskReponseType.yesButtonClicked | AskReponseType.noButtonClicked };
export type AskFollowupQuestionResultType = { content: string };
export type CtagsQueryResultType = {
	rawLineContent: string;
	name: string;
	path: string;
	scopePath: string;
	line: number;
	kind: string;
	language: string;
	positions?: [number, number];
}[];
export type ClangdQueryResultType = {
	uri: string;
	range: {
		start: { line: number; character: number };
		end: { line: number; character: number };
	};
}[];
export type ShowContentType = { content: string };
export type ExecCommandResultType = { output: string };

export type ToolCallReturnType = {
	[ToolNameEnum.READ_FILE]: ReadFileResultType;
	[ToolNameEnum.EDIT_FILE]: void;
	[ToolNameEnum.LIST_FILES]: ListFilesResultType;
	[ToolNameEnum.PATHNAME_SEARCH]: PathnameSearchResultType;
	[ToolNameEnum.SEARCH]: SearchResultType;
	[ToolNameEnum.CREATE_FILE]: CreateFileResultType;
	[ToolNameEnum.UPDATE_FILE]: UpdateToFileResultType;
	[ToolNameEnum.APPROVE_REQUEST]: ApproveRequestResultType;
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: AskFollowupQuestionResultType;
	[ToolNameEnum.CTAGS_QUERY]: CtagsQueryResultType;
	[ToolNameEnum.CLANGD_QUERY]: ClangdQueryResultType;
	[ToolNameEnum.SHOW_SUMMARY]: void;
	[ToolNameEnum.SHOW_CONTENT]: ShowContentType;
	[ToolNameEnum.CODEBASE_SEARCH]: ISearchResult[];
	[ToolNameEnum.EXEC_COMMAND]: ExecCommandResultType;
};

export type DirectoryItem = {
	uri: URI;
	name: string;
	isDirectory: boolean;
	isSymbolicLink: boolean;
};

// export type ToolNeedApprove = { [T in ToolName & keyof TooLNeedApproveBool]: TooLNeedApproveBool[T] };
export type ToolFns = { [T in ToolName]: (p: ToolCallParamsType[T], callback?: () => any, cancellationToken?: CancellationToken) => Promise<ToolCallReturnType[T]> };
export type ToolResultToString = { [T in ToolName]: (result: ToolCallReturnType[T]) => string };
